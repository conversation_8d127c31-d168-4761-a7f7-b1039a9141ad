# %% [markdown]
# # Quick START
# 
# ```python
# # 准备立方体 X: [F, D, T, N]
# #    - F: 特征数
# #    - D: 天数
# #    - T: 每天分钟数（固定 241）
# #    - N: 股票数（固定 6000）
# # X = np.load("/path/to/X_fdtN.npy")  

# %% [markdown]
# 2.1 形状与轴语义
# 
# 输入数组 X 的形状：[F, D, T, N]
# 
# F：特征数（features），例如 8、16、32…
# 
# D：天数（days），例如 1000
# 
# T：固定 241（一天内分钟数；你的业务设定）
# 
# N：股票数（固定 6000；顺序固定且已知）
# 
# 缺失表示：用 np.nan 表示“该日该股（或该分钟）无数据”。
# 
# 2.2 有效性规则（关键）
# 
# 整天有效规则（与你的数据保持一致）：
# 
# 若某只股票在某一天存在任意一个分钟（或任意一个特征）为 NaN，则判定为该股票在该天无效。
# 
# 内部实现为：
# 
# `# X_dtnf: [D, T, N, F]`
# 
# valid_dn = np.isfinite(X_dtnf).all(axis=(1, 3))  # over (T, F) → [D, N]
# 
# 因此，当天的 N_valid 对一天内所有分钟一致。

# %% [markdown]
# ## MinuteCube

# %%
import numpy as np
from typing import Tuple, Optional, List, Iterator
import torch
from torch.utils.data import Dataset, Sampler, DataLoader

# ---------- 基础包装：把 [F, D, T, N] 视图转到 [D, T, N, F] ----------
class MinuteCube:
    """
    仅管理数据视图与有效性掩码，不做任何 pandas 操作。
    输入: X [F, D, T, N]
    内部主视图: X_dtnf [D, T, N, F] (zero-copy view via moveaxis)
    """
    def __init__(self, X_fdtN: np.ndarray, assume_nan_invalid: bool = True):
        assert X_fdtN.ndim == 4, "Expect [F, D, T, N]"
        self.X_dtnf = np.moveaxis(X_fdtN, (0,1,2,3), (3,0,1,2))  # -> [D, T, N, F]
        self.D, self.T, self.N, self.F = self.X_dtnf.shape
        self.assume_nan_invalid = assume_nan_invalid
        # 整天有效（若输入还有 NaN，占位将被排除）
        self.valid_dn = np.isfinite(self.X_dtnf).all(axis=(1, 3))  # [D, N]
        self.valid_lists: List[np.ndarray] = [np.nonzero(self.valid_dn[d])[0] for d in range(self.D)]

    @staticmethod
    def from_fdtN(X_fdtN: np.ndarray, assume_nan_invalid: bool = True) -> "MinuteCube":
        return MinuteCube(X_fdtN, assume_nan_invalid=assume_nan_invalid)

    def day_slice(self, d: int) -> np.ndarray:
        return self.X_dtnf[d]  # [T, N, F]

    def minute_slice(self, d: int, t: int) -> np.ndarray:
        return self.X_dtnf[d, t]  # [N, F]

    def split_days(self, train_ratio: float = 0.8) -> Tuple["MinuteCube", "MinuteCube", np.ndarray, np.ndarray]:
        """
        按天时间顺序拆分 -> (cube_train, cube_valid, idx_train, idx_valid)
        返回的两个 cube 是“视图”（不复制）。
        """
        D_train = int(self.D * train_ratio)
        idx_train = np.arange(D_train, dtype=np.int64)
        idx_valid = np.arange(D_train, self.D, dtype=np.int64)
        # 视图切片（把 [D,T,N,F] 切出来再 moveaxis 回 [F,D,T,N] 供构造器使用；都是视图，不拷贝）
        X_tr_fdtN = np.moveaxis(self.X_dtnf[idx_train], (3,0,1,2), (0,1,2,3))
        X_va_fdtN = np.moveaxis(self.X_dtnf[idx_valid], (3,0,1,2), (0,1,2,3))
        cube_train = MinuteCube(X_tr_fdtN)
        cube_valid = MinuteCube(X_va_fdtN)
        return cube_train, cube_valid, idx_train, idx_valid

# %% [markdown]
# ## DaySectionDataset

# %%
class DaySectionDataset(Dataset):
    """
    单位: 一天的全截面 -> [N_valid, T, F]
    注意: __getitem__ 返回可变形状 (N_valid 不同)，因此配合 batch_sampler 每次只取一天。
    """
    def __init__(self, cube: MinuteCube):
        self.cube = cube

    def __len__(self) -> int:
        return self.cube.D  # Days

    def __getitem__(self, d: int) -> np.ndarray:
        X_TNF = self.cube.day_slice(d)            # [T, N, F] view
        idx_n = self.cube.valid_lists[d]          # [N_valid]
        # 先取 N 维，再换轴到 [N_valid, T, F]
        x = X_TNF[:, idx_n, :]                    # [T, N_valid, F]
        x = np.moveaxis(x, 0, 1)                  # -> [N_valid, T, F]
        return x  # numpy array (float), 可直接转 torch.tensor 再返回也行


class DayBatchSampler(Sampler[List[int]]):
    """
    保证 DataLoader 一次只取一天（即一个样本即一批）。
    """
    def __init__(self, days: int, shuffle: bool = False, rng: Optional[np.random.Generator] = None):
        self.days = days
        self.shuffle = shuffle
        self.rng = rng or np.random.default_rng()

    def __iter__(self) -> Iterator[List[int]]:
        idx = np.arange(self.days)
        if self.shuffle:
            self.rng.shuffle(idx)
        for d in idx:
            yield [int(d)]  # 一次只给一个 day 索引，等价于“一天一个 batch”

    def __len__(self) -> int:
        return self.days


def passthrough_collate(batch):
    """
    传入 batch=[x]，直接返回 x；避免 DataLoader 把变长 N_valid 的天拼一起。
    """
    assert len(batch) == 1
    return batch[0]


# %% [markdown]
# ## PairDataset

# %%
class PairDataset(Dataset):
    """
    最小单位: (day, instrument) -> [T, F]
    可直接 batch 成 [B, T, F]
    """
    def __init__(self, cube: MinuteCube):
        self.cube = cube
        D, T, N, F = cube.X_dtnf.shape
        # 扁平化所有 (d, n) 对，仅保留当天有效的
        dn_mask = cube.valid_dn  # [D, N]
        self.d_idx, self.n_idx = np.nonzero(dn_mask)  # 两个等长一维数组
        self.length = self.d_idx.shape[0]

    def __len__(self) -> int:
        return self.length

    def __getitem__(self, i: int) -> np.ndarray:
        d = int(self.d_idx[i]); n = int(self.n_idx[i])
        x = self.cube.X_dtnf[d, :, n, :]  # [T, F], view
        return x  # numpy array (float)




# %% [markdown]
# ## MinuteLookbackWithinDayDataset

# %%
class MinuteLookbackWithinDayDataset(Dataset):
    """
    锚点: (day=d, minute=t)
    返回: [N_valid, L, F]
    规则: 同日内按分钟回看；不足 L 分钟用首分钟重复（索引层 clip 到 0）
    """
    def __init__(self, cube: MinuteCube, L: int, filter_valid: bool = True):
        self.cube = cube
        self.L = int(L)
        self.D, self.T, self.N, self.F = cube.X_dtnf.shape
        self.filter_valid = filter_valid
        # 预计算“分钟窗口索引”：win[t] = [max(0, t-L+1), ..., t]  -> [T, L]
        self.win = np.stack([
            np.clip(np.arange(t - self.L + 1, t + 1, dtype=np.int64), 0, t)
            for t in range(self.T)
        ], axis=0)  # [T, L]
        self.length = self.D * self.T

    def __len__(self) -> int:
        return self.length  # Days * T

    def _unravel(self, i: int) -> Tuple[int, int]:
        d = i // self.T
        t = i %  self.T
        return d, t

    def __getitem__(self, i: int) -> np.ndarray:
        d, t = self._unravel(i)
        minutes = self.win[t]                         # [L]
        # 先取 [L, N, F]（同日内不同分钟拼窗口），再转到 [N, L, F]
        x = self.cube.X_dtnf[d, minutes, :, :]       # [L, N, F]
        x = np.moveaxis(x, 0, 1)                     # -> [N, L, F]
        if self.filter_valid:
            idx_n = self.cube.valid_lists[d]         # 该日有效股票（整天有效）
            x = x[idx_n, :, :]                       # -> [N_valid, L, F]
        return x

class MinuteBatchSampler(Sampler[List[int]]):
    """
    每次只发出一个 (d,t) 样本索引，方便 collate 透传。
    """
    def __init__(self, D: int, T: int, shuffle: bool = False, rng: Optional[np.random.Generator] = None):
        self.D, self.T = D, T
        self.shuffle = shuffle
        self.rng = rng or np.random.default_rng()

    def __iter__(self) -> Iterator[List[int]]:
        idx = np.arange(self.D * self.T)
        if self.shuffle:
            self.rng.shuffle(idx)
        for i in idx:
            yield [int(i)]

    def __len__(self) -> int:
        return self.D * self.T


# %% [markdown]
# ## MinuteLookbackAcrossDaysDataset

# %%
class MinuteLookbackAcrossDaysDataset(Dataset):
    """
    锚点: (day=d, minute=t)，展平成全局分钟 g = d*T + t
    窗口: 全局分钟 [g-L+1, ..., g]，支持跨天
    返回: [N_sel, L, F]（N_sel 由过滤策略决定）

    过滤策略 filter_mode:
        - 'anchor': 只保留锚点当天整天有效的股票（N_sel = 当天 N_valid）
        - 'window': 保留窗口覆盖的所有天都整天有效的股票（跨天取交集）
        - 'none'  : 不过滤（返回 N 全量）

    边界填充 pad_mode:
        - 'repeat': 把 (g-L+1) < 0 的部分裁到 0，相当于“重复最早分钟”
        - 'nan'   : 允许窗口头部落在负索引，输出直接以 NaN 占位（实现为先 repeat，再另行置 NaN）
                   （默认推荐 repeat，简单高效）

    备注：
    - 对于股票在窗口内某些天无效（整天 NaN）的情况，若使用 'window' 过滤，会在股票维过滤掉；
      若使用 'anchor'，则不强制窗口内其他天有效，窗口中会出现 NaN（模型端需能处理或再做填充）。
    """
    def __init__(
        self,
        cube: MinuteCube,
        L: int,
        filter_mode: str = "anchor",     # 'anchor' | 'window' | 'none'
        pad_mode: str = "repeat",        # 'repeat' | 'nan'
        precompute_windows: bool = True  # 为速度预计算 [G, L] 的全局窗口索引
    ):
        self.cube = cube
        self.L = int(L)
        self.filter_mode = filter_mode
        self.pad_mode = pad_mode
        self.D, self.T, self.N, self.F = cube.X_dtnf.shape

        # 全局分钟数 G
        self.G = self.D * self.T

        # 预计算锚点对应的 (d, t)
        g = np.arange(self.G, dtype=np.int64)
        self.g2d = g // self.T
        self.g2t = g %  self.T

        # 预计算窗口全局索引 [G, L]
        if precompute_windows:
            # 基础窗口（不裁剪）
            base = np.arange(-self.L + 1, 1, dtype=np.int64)  # [-L+1, ..., 0]
            gw = g[:, None] + base[None, :]                   # [G, L]
            if self.pad_mode == "repeat":
                # 负索引裁到 0
                gw = np.clip(gw, 0, None)
            elif self.pad_mode == "nan":
                # 先裁到 0，稍后在 __getitem__ 中再将 gw<0 的位置置 NaN
                gw_clip = np.clip(gw, 0, None)
                self._gw_neg_mask = (gw < 0)
                gw = gw_clip
            else:
                raise ValueError(f"pad_mode {self.pad_mode} not supported")
            self.g_windows = gw  # [G, L]
        else:
            self.g_windows = None  # 运行时现算

    def __len__(self) -> int:
        return self.G  # 全局分钟数（Days * T）

    def _window_indices(self, g: int):
        if self.g_windows is not None:
            gw = self.g_windows[g]  # view: [L]
            gw_neg_mask = None
            if self.pad_mode == "nan":
                gw_neg_mask = getattr(self, "_gw_neg_mask")[g]
            return gw, gw_neg_mask
        # 现算一条
        base = np.arange(g - self.L + 1, g + 1, dtype=np.int64)  # [L]
        if self.pad_mode == "repeat":
            base = np.clip(base, 0, None)
            return base, None
        elif self.pad_mode == "nan":
            neg_mask = base < 0
            base = np.clip(base, 0, None)
            return base, neg_mask
        else:
            raise ValueError

    def __getitem__(self, i: int) -> np.ndarray:
        # i 是锚点全局分钟 g
        g = int(i)
        d = int(self.g2d[g])  # 锚点当天
        # 取窗口的全局分钟索引
        gw, neg_mask = self._window_indices(g)           # [L], (optional) [L] bool
        days   = (gw // self.T).astype(np.int64)         # [L]
        minutes= (gw %  self.T).astype(np.int64)         # [L]

        # 取原始数据 [L, N, F]，再转为 [N, L, F]
        x = self.cube.X_dtnf[days, minutes, :, :]        # [L, N, F]
        x = np.moveaxis(x, 0, 1)                         # -> [N, L, F]

        # pad_mode = 'nan' 时，把越界头部置 NaN
        if neg_mask is not None and np.any(neg_mask):
            # 将这些位置（窗口头部）置 NaN
            x[:, neg_mask, :] = np.nan

        # 过滤策略
        if self.filter_mode == "none":
            return x  # [N, L, F]

        elif self.filter_mode == "anchor":
            # 仅保留锚点当天整天有效的股票
            idx_n = self.cube.valid_lists[d]             # [N_valid(d)]
            return x[idx_n, :, :]                        # [N_valid, L, F]

        elif self.filter_mode == "window":
            # 窗口覆盖的所有天都整天有效（交集）
            mask_n = self.cube.valid_dn[days].all(axis=0)  # [N]
            idx_n = np.nonzero(mask_n)[0]
            return x[idx_n, :, :]                          # [N_sel, L, F]

        else:
            raise ValueError(f"filter_mode {self.filter_mode} not supported")


class GlobalMinuteBatchSampler(Sampler[List[int]]):
    def __init__(self, G: int, shuffle: bool = False, rng: Optional[np.random.Generator] = None):
        self.G = int(G)
        self.shuffle = shuffle
        self.rng = rng or np.random.default_rng()

    def __iter__(self) -> Iterator[List[int]]:
        idx = np.arange(self.G, dtype=np.int64)
        if self.shuffle:
            self.rng.shuffle(idx)
        for i in idx:
            yield [int(i)]

    def __len__(self) -> int:
        return self.G

def passthrough_collate(batch):
    assert len(batch) == 1
    return batch[0]

# %% [markdown]
# ## stand

# %%
import numpy as np
from typing import Tuple, Optional, List, Iterator
import gc
import torch
from torch.utils.data import Dataset, Sampler, DataLoader


# ---------- 标准化器：只做“拟合/应用标准化”，不做任何填充 ----------
class CubeStandardizer:
    """
    mode:
      - 'global_f'   : 每个特征 F 一个 (mu, sd)；沿 (D,T,N) 计算，使用 nanmean/nanstd 忽略占位 NaN。
      - 'per_stock_f': 每个 (N,F) 一个 (mu, sd)；沿 (D,T) 计算，使用 nanmean/nanstd；
                       对“训练期未出现的新股票/占位股票”或 std 太小的条目，回退到全局 (mu_g, sd_g)。

    属性（便于检查/过滤）:
      - stock_seen: [N] 训练期是否见过任意特征的有效值
      - per_stock_seen: [N,F] 训练期该 (n,f) 是否见过有效值
    """
    def __init__(self, mode: str = "per_stock_f", eps: float = 1e-6):
        assert mode in ("global_f", "per_stock_f")
        self.mode = mode
        self.eps = eps
        self.mu = None   # [F] or [N,F] (float32)
        self.sd = None   # [F] or [N,F] (float32)
        self.mu_g = None # [F]（全局统计，per_stock_f 回退时用）
        self.sd_g = None # [F]
        self.fitted = False
        self.stock_seen = None       # [N]
        self.per_stock_seen = None   # [N,F]

    def fit(self, cube_train: MinuteCube) -> "CubeStandardizer":
        X = cube_train.X_dtnf  # [D,T,N,F]
        D, T, N, F = cube_train.D, cube_train.T, cube_train.N, cube_train.F

        # --- 全局统计：沿 (D,T,N) 用 nanmean/nanstd，兜底 (mu=0, sd=1) ---
        import warnings # Mean/Std of empty slice is our desired behavior
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", category=RuntimeWarning)
            mu_g = np.nanmean(X, axis=(0,1,2))        # [F]
            sd_g = np.nanstd (X, axis=(0,1,2), ddof=0)
        mu_g = np.where(np.isfinite(mu_g), mu_g, 0.0).astype(np.float32)
        sd_g = np.where(np.isfinite(sd_g) & (sd_g >= self.eps), sd_g, 1.0).astype(np.float32)
        self.mu_g, self.sd_g = mu_g, sd_g

        if self.mode == "global_f":
            self.mu, self.sd = mu_g, sd_g
            self.fitted = True
            # 可选：seen 信息
            with warnings.catch_warnings():
                warnings.simplefilter("ignore", category=RuntimeWarning)
                self.per_stock_seen = np.isfinite(X).any(axis=(0,1))  # [N,F]
                self.stock_seen = self.per_stock_seen.any(axis=1)     # [N]
            return self

        # --- per_stock_f：沿 (D,T) 对每个 (N,F) 统计；对未见过/退化条目回退到全局 ---
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", category=RuntimeWarning)
            # 训练期可见性：某 (n,f) 是否出现过有限值
            per_stock_seen = np.isfinite(X).any(axis=(0,1))  # [N,F]
            stock_seen = per_stock_seen.any(axis=1)          # [N]
            mu_ps = np.nanmean(X, axis=(0,1))                # [N,F]
            sd_ps = np.nanstd (X, axis=(0,1), ddof=0)        # [N,F]

        # 无数据或 NaN → 回退到全局；方差过小也回退到全局 sd
        invalid_mu = (~np.isfinite(mu_ps)) | (~per_stock_seen)
        invalid_sd = (~np.isfinite(sd_ps)) | (sd_ps < self.eps) | (~per_stock_seen)
        mu_ps = np.where(invalid_mu, mu_g[None, :], mu_ps)
        sd_ps = np.where(invalid_sd, sd_g[None, :], sd_ps)

        self.mu  = mu_ps.astype(np.float32)  # [N,F]
        self.sd  = sd_ps.astype(np.float32)  # [N,F]
        self.per_stock_seen = per_stock_seen
        self.stock_seen = stock_seen
        self.fitted = True
        return self

    def transform(
        self,
        cube: MinuteCube,
        out_dtype: np.dtype = np.float32,
        out_memmap_path: Optional[str] = None,
    ) -> MinuteCube:
        assert self.fitted, "Call fit() on train cube first."
        D, T, N, F = cube.D, cube.T, cube.N, cube.F
        shape = (D, T, N, F)

        # 申请输出（数组或 memmap）
        if out_memmap_path is None:
            X_out = np.empty(shape, dtype=out_dtype)
        else:
            X_out = np.memmap(out_memmap_path, mode="w+", dtype=out_dtype, shape=shape)

        # 拷贝并做广播标准化（不会改变 NaN，占位 NaN 会保持 NaN，后续由 valid_dn 过滤）
        np.copyto(X_out, cube.X_dtnf, casting="unsafe")

        if self.mode == "global_f":
            # [D,T,N,F] - [1,1,1,F]
            X_out -= self.mu[None, None, None, :]
            X_out /= self.sd[None, None, None, :]
        else:  # per_stock_f
            # [D,T,N,F] - [1,1,N,F]；对于“未见过的新股票/占位股票”，mu/sd 已在 fit 阶段回退为全局统计
            X_out -= self.mu[None, None, :, :]
            X_out /= self.sd[None, None, :, :]

        # 返回新的 MinuteCube（构造器需要 [F,D,T,N]）
        return MinuteCube(np.moveaxis(X_out, (3,0,1,2), (0,1,2,3)))


# %% [markdown]
# ## Test

# %%
# X: [F, D, T, N]
X = np.load("projs/stock1m/data/OHLCVA_Vwap_cube.npy") # change to true dir


# %%
# 假设你已完成“表格 -> cube”清洗（无脏 NaN，只有占位 NaN）
# X: np.ndarray [F, D, T, N], dtype=float32
cube_all = MinuteCube.from_fdtN(X)

# 1) 拆分（时间顺序）
cube_train_view, cube_valid_view, idx_tr, idx_va = cube_all.split_days(train_ratio=0.8)

# 2) 在训练集拟合标准化（建议 per_stock_f）
std = CubeStandardizer(mode="per_stock_f", eps=1e-6).fit(cube_train_view)

# 3) 分别变换得到新的 cube（数组/或 memmap）
cube_train = std.transform(cube_train_view)                          # MinuteCube
cube_valid = std.transform(cube_valid_view)                          # MinuteCube
# 若内存紧张：
# cube_valid = std.transform(cube_valid_view, out_memmap_path="/tmp/cube_valid.dat")

# 4) 释放原数组与视图（节省内存）
del X, cube_all, cube_train_view, cube_valid_view
gc.collect()

# 5) 下游 DataLoader（按天整截面）
ds_tr = DaySectionDataset(cube_train)
dl_tr = DataLoader(ds_tr, batch_sampler=DayBatchSampler(len(ds_tr), shuffle=True), collate_fn=passthrough_collate)

ds_va = DaySectionDataset(cube_valid)
dl_va = DataLoader(ds_va, batch_sampler=DayBatchSampler(len(ds_va), shuffle=False), collate_fn=passthrough_collate)

# （可选）如果只想评估“训练期见过的股票”，可用 std.stock_seen 进一步过滤：
#   dn_mask = cube_valid.valid_dn & std.stock_seen[None, :]
#   然后在 DaySectionDataset 中按 dn_mask[d] 选择股票。


# %%
for data in dl_tr:
    print(data.shape)

# %% [markdown]
# ### DaySectionDataset

# %%
cube = MinuteCube(X)
ds_day = DaySectionDataset(cube)
sampler = DayBatchSampler(days=len(ds_day), shuffle=False)
loader_day = DataLoader(ds_day, batch_sampler=sampler, collate_fn=passthrough_collate, num_workers=0)

print(len(loader_day)) # 61 days

# %%
# 迭代：
for x_day in loader_day:
    print(x_day.shape)

    # x_day: [N_valid, T, F] (numpy)，如需 torch，则 torch.from_numpy(x_day.copy()) 以防 pin 内存问题


# %% [markdown]
# ### PairDataset

# %%
ds_pair = PairDataset(cube)
loader_pair = DataLoader(ds_pair, batch_size=64, shuffle=False, num_workers=0)  # 返回 [B, T, F]

print(len(loader_pair)) # ~ 61 days * (5000 symbols / 64 per batch) = 4200


# %%
for x in loader_pair:
    print(x.shape)
    break
    # x: torch 默认会把 numpy 自动转 torch？不会，PyTorch DataLoader不会自动转。
    # 如果你需要 torch.Tensor，可自定义 collate_fn：把 batch 的 numpy 堆起来再转 torch.from_numpy。
    #pass


# %% [markdown]
# ### MinuteLookbackWithinDayDataset

# %%
cube = MinuteCube(X)                     # X: [F, D, T, N]
ds_lb = MinuteLookbackWithinDayDataset(cube, L=16, filter_valid=True)
sampler = MinuteBatchSampler(cube.D, cube.T, shuffle=False)
loader_lb = DataLoader(ds_lb, batch_sampler=sampler, collate_fn=passthrough_collate, num_workers=0)

print(len(loader_lb)) # 61 days * 241 mins = 14701


# %%
for x in loader_lb:
    # x: [N_valid, L, F]
    print(x)
    break

# %% [markdown]
# ### MinuteLookbackAcrossDaysDataset

# %%
# X: [F, D, T, N]
cube = MinuteCube(X)

# 跨天分钟回看：L=16，锚点当天过滤（N_sel = 当天 N_valid）
ds = MinuteLookbackAcrossDaysDataset(
    cube, L=16,
    filter_mode="anchor",  # 或 "window" / "none"
    pad_mode="repeat",     # 或 "nan"
    precompute_windows=True
)
sampler = GlobalMinuteBatchSampler(ds.G, shuffle=False)
loader  = DataLoader(ds, batch_sampler=sampler, collate_fn=passthrough_collate, num_workers=0)



for x in loader:
    # x: [N_sel, L, F]
    print(x.shape)




# %%



