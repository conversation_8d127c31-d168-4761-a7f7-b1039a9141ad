from typing import List
from .utils import *
from .lossfuncs import *
from collections import defaultdict
from fastprogress.fastprogress import master_bar, progress_bar
import torch.nn.functional as F
import torch.nn as nn
import numpy as np
import torch
import os
from torch.autograd import Variable
from DLlib.optimizer.min_norm_solvers import MinNormSolver, gradient_normalizers
from DLlib.optimizer.famo import FAMO



class ModelTrainer:

    def __init__(
        self,
        model,
        optimizer,
        logger,
        # lr_scheduler=None,
        loss: List = [nn.L1Loss()],
        metric: List = [nn.L1Loss()],
        num_task=1,  # 多目标问题
        multi_task_optimize: str | None = None,  # 多目标问题是否采用pareto优化, 目前仅支持MGDA, FAMO
        early_stop=5,  # 早停patience
        n_epochs=100,
        name="Model",
        gpu=0,  # 使用第几块gpu，小于0则使用cpu
    ):
        self.logger = logger
        self.name = name
        self.logger.info(f"{name} pytorch version...")

        self.device = get_device(gpu)
        self.logger.info("device using: {}".format(self.device))

        self.model = model
        self.model.to(self.device)
        self.logger.info(self.model)

        self.loss = loss
        self.metric = metric

        self.num_task = num_task
        self.n_epochs = n_epochs

        self.optimizer = optimizer
        self.multi_task_optimize = multi_task_optimize
        if self.multi_task_optimize == "FAMO":
            self.weight_opt = FAMO(n_tasks=num_task, device=self.device)

        self.fitted = False  # self.model 是否是已经训练好的模型
        self.early_stop = early_stop
        self.early_stopper = EarlyStopper(
            patience=self.early_stop,
            verbose=True,
            delta=1e-6,
            direction="min",
            logger=self.logger,
        )

    @property
    def use_gpu(self):
        return self.device != torch.device("cpu")

    @staticmethod
    def get_loss(loss_func, task_preds, task_label, weight=None):
        if isinstance(loss_func, MultiClassFocalLoss):
            l = loss_func(task_preds.float(), task_label.float())
        else:
            l = loss_func(task_preds.float(), task_label.float(), weight)
        return l

    def _train_epoch(self, data_loader, mb):
        """
        mb: 进度条嵌套的控制参数
        """
        self.model.train()
        count = -1
        for batch in progress_bar(data_loader, parent=mb):
            feature = batch["features"].to(self.device)  # [B, S, F]
            label = batch["label"].to(self.device)  # [B, self.num_task] (ts) or [B, S, self.num_task] (cs)
            weight = batch["weight"].to(self.device)  # [B] (ts) or [B, S] (cs)
            count += 1
            mb.child.comment = "Training..."
            # !! preds and label should be in a LIST form to support multi tasks.
            preds = self.model(feature.float())
            if not isinstance(preds, list):
                preds = [preds]
            if self.multi_task_optimize == "MGDA":
                loss_data = {}
                grads = {}
                scale = {}
                # This is MGDA
                for i in range(self.num_task):
                    # Comptue gradients of each loss function wrt parameters
                    self.optimizer.zero_grad()
                    loss = self.get_loss(self.loss[i], preds[i], label[..., i], weight)
                    loss_data[i] = loss.data
                    loss.backward(retain_graph=True)  # 每个loss都需要backward一次
                    grads[i] = []
                    for param in self.model.parameters():
                        if param.grad is not None:
                            grads[i].append(Variable(param.grad.data.clone(), requires_grad=False))
                # Normalize all gradients, this is optional and not included in the paper.
                gn = gradient_normalizers(grads, loss_data, "l2")
                for i in range(self.num_task):
                    for gr_i in range(len(grads[i])):
                        grads[i][gr_i] = grads[i][gr_i] / gn[i]
                # Frank-Wolfe iteration to compute scales.
                sol, min_norm = MinNormSolver.find_min_norm_element([grads[i] for i in range(self.num_task)])
                for i in range(self.num_task):
                    scale[i] = float(sol[i])
                # Scaled back-propagation
                self.optimizer.zero_grad()
                for i in range(self.num_task):
                    l = self.get_loss(self.loss[i], preds[i], label[..., i], weight)
                    loss_data[i] = l.item()
                    loss = loss + scale[i] * l if i > 0 else scale[i] * l
                loss.backward()
                nn.utils.clip_grad_value_(self.model.parameters(), 3.0)  # 梯度裁剪
                self.optimizer.step()
            else:
                loss = [self.get_loss(self.loss[i], preds[i], label[..., i], weight) for i in range(self.num_task)]
                if self.multi_task_optimize == "FAMO":
                    loss = torch.stack(loss, dim=0)
                    self.optimizer.zero_grad()
                    self.weight_opt.backward(loss)
                    nn.utils.clip_grad_value_(self.model.parameters(), 3.0)  # 梯度裁剪
                    self.optimizer.step()
                    # update the task weighting
                    with torch.no_grad():
                        preds = self.model(feature.float())
                        if not isinstance(preds, list):
                            preds = [preds]
                        new_loss = torch.stack(
                            [
                                self.get_loss(self.loss[i], preds[i], label[..., i], weight)
                                for i in range(self.num_task)
                            ],
                            dim=0,
                        )
                        self.weight_opt.update(new_loss)
                else:
                    loss = sum(loss)
                    self.optimizer.zero_grad()
                    loss.backward()
                    # nn.utils.clip_grad_value_(self.model.parameters(), 3.0)  # 梯度裁剪
                    self.optimizer.step()

            # # 调试
            # print("current loss is {}".format(loss))
            # for name, param in self.model.named_parameters():
            #     if param.grad is None or torch.all(torch.isnan(param.grad)):
            #         print(f"No gradient for {name}")
            #         sys.exit()
            #     else:
            #         print(f"Gradient of {name}:\n{param.grad}")

            # if self.lr_scheduler != None:  # 学习率调整策略
            #     evals = self.evals_result["valid_loss"]
            #     evals = evals[-1][-1] if len(evals) != 0 else np.inf
            #     self.lr_scheduler.step(evals)
            #     # if self.batch_num % 256 == 0:
            #     #     self.logger.info("current learning rate is {}".format(self.lr_scheduler.get_last_lr()[0]))
            # self.batch_num += 1

    def _test_epoch(self, data_loader, mb, ds="train"):
        self.model.eval()
        # 前面分别存储每一类的loss，最后一个存储总loss，
        loss_value = [[] for _ in range(1 + self.num_task)]
        metric_value = [[] for _ in range(1 + self.num_task)]
        for batch in progress_bar(data_loader, parent=mb):
            feature = batch["features"].to(self.device)
            label = batch["label"].to(self.device)
            mb.child.comment = "validating..."
            preds = self.model(feature.float())
            if not isinstance(preds, list):
                preds = [preds]
            losses = []
            for i in range(self.num_task):
                tmp_loss = self.get_loss(self.loss[i], preds[i], label[..., i])
                losses.append(tmp_loss)
                loss_value[i].append(tmp_loss.item())
            loss = sum(losses)
            loss_value[self.num_task].append(loss.item())

            if self.metric != "":
                metrics = []
                for i in range(self.num_task):
                    tmp_metric = self.get_loss(self.metric[i], preds[i], label[..., i])
                    metrics.append(tmp_metric)
                    metric_value[i].append(tmp_metric.item())
                metric = sum(metrics)
                metric_value[self.num_task].append(metric.item())

        loss_value = [np.mean(x) for x in loss_value]
        loss_info = f"{ds.upper()} LOSS: total={loss_value[-1]:.6f};"
        if len(loss_value) > 2:  # MTL
            for i in range(self.num_task):
                loss_info = loss_info + f" task{i + 1}={loss_value[i]:.6f};"
        self.logger.info(loss_info)
        # self.evals_result[f"{ds}_loss"].append(loss_value)

        if self.metric != "":
            # 1-ir as metrics
            # metric_value = [1 - ((1 - np.mean(x)) / (np.std(x)+1e-4)) for x in metric_value]
            metric_value = [np.mean(x) for x in metric_value]
            metric_info = f"{ds.upper()} METRIC: total={metric_value[-1]:.6f};"
            if len(metric_value) > 2:
                for i in range(self.num_task):
                    metric_info = metric_info + f" task{i + 1}={metric_value[i]:.6f};"
            self.logger.info(metric_info)
            # self.evals_result[f"{ds}_metric"].append(metric_value)

        if ds == "valid":  # 早停，调用一次earlystop才有可能会积累一次patience
            if self.metric != "":
                self.early_stopper.earlystop(value=metric_value[-1], model=self.model)
            else:
                self.early_stopper.earlystop(value=loss_value[-1], model=self.model)
        return metric_value[-1] if self.metric != "" else loss_value[-1]

    def fit(self, train_loader, valid_loader, save_path):
        """外部调用的训练函数"""
        # self.evals_result = defaultdict(list)  # 存储 train_loss, valid_loss, train_metric, valid_metric
        self.logger.info("Train Begining...")
        self.batch_num = 0

        mb = master_bar(range(self.n_epochs))
        for step in mb:
            self.logger.info(f"EPOCH {step + 1}")
            self._train_epoch(train_loader, mb)
            # self._test_epoch(train_loader, mb, ds='train')  # 不会再次计算train_loss，加快训练
            with torch.no_grad():
                self._test_epoch(valid_loader, mb, ds="valid")

            # 对于某些特别的损失函数早停到达一半时，更新损失函数
            if self.early_stopper.count >= self.early_stop // 2:
                [l.update_lossfunc() for l in self.loss if isinstance(l, EnsembleSwitchLoss)]

            if self.early_stopper.is_earlystop:
                self.logger.info("best loss: %.6lf" % (self.early_stopper.best_value))
                self.model.load_state_dict(self.early_stopper.best_model)
                break

        torch.save(self.model.state_dict(), save_path)
        self.fitted = True
        if self.use_gpu:
            torch.cuda.empty_cache()
        # self.evals_result = {
        #     k: pd.DataFrame(data=v, index=range(1, 2+step), columns=[f"task{i+1}" for i in range(self.num_task)] + ["total_%s"%k, ])
        #     for k, v in self.evals_result.items()
        # }

    def load_model(self, path):
        print("load model at %s" % path)
        self.model.load_state_dict(torch.load(path))
        self.fitted = True

    def predict(self, test_loader):
        if not self.fitted:
            raise ValueError("model is not fitted yet!")
        self.model.eval()
        preds = [[] for _ in range(self.num_task)]
        labels = [[] for _ in range(self.num_task)]  # 原始label
        signs = []  # 标记输出的股票、时间
        for batch in progress_bar(test_loader):
            end_idx = batch["end_idx"]
            with torch.no_grad():
                outs = self.model(batch["features"].to(self.device).float())
                if not isinstance(outs, list):
                    outs = [outs]
            label = batch["label"]
            for i in range(self.num_task):
                preds[i].append(outs[i].detach().cpu().numpy())
                labels[i].append(label[..., i].detach().cpu().numpy())
            if isinstance(end_idx, List):  # [N, batch_size]
                signs.append(torch.sort(torch.cat(end_idx)).values.detach().cpu().numpy())
            else:
                signs.append(end_idx.detach().cpu().numpy())
        preds = [np.concatenate(x, axis=0).reshape(-1) for x in preds]
        signs = np.concatenate(signs, axis=0)
        labels = [np.concatenate(x, axis=0).reshape(-1) for x in labels]
        return preds, labels, signs
