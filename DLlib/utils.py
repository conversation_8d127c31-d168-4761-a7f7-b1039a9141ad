import torch
import numpy as np
import random
import os
from copy import deepcopy


def winsorize(x, lower=0.01, upper=0.99):
    lower_bound = np.quantile(x, lower)
    upper_bound = np.quantile(x, upper)
    return np.clip(x, lower_bound, upper_bound)


def get_device(cuda_number):
    if torch.cuda.device_count() >= cuda_number + 1:
        device = torch.device(f"cuda:{cuda_number}")
    else:
        device = torch.device("cpu")
    return device





def create_time_weights(n_samples, decay_factor=0.95):
    """Create exponentially decaying weights based on sample position."""
    positions = np.arange(n_samples)
    normalized_positions = positions / (n_samples - 1)
    weights = decay_factor ** (1 - normalized_positions)
    weights = weights * n_samples / weights.sum()
    return weights


def set_seed(seed: int = 0):
    random.seed(seed)
    os.environ["PYTHONHASHSEED"] = str(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.deterministic = True
    print("Set random seed as {} for pytorch".format(seed))


class EarlyStopper:
    def __init__(
        self,
        patience=5,
        verbose=False,
        delta=0,
        direction="min",
        logger=None,
        accelerator=None,
    ):

        self.patience = patience
        self.verbose = verbose
        self.delta = delta
        self.direction = 1 if direction == "min" else -1
        self.logger = logger
        self.accelerator = accelerator
        self.best_value = None
        self.is_earlystop = False
        self.count = 0
        self.best_model = None

    def earlystop(self, value: float, model=None):
        """
        value : float 评价分数
        model : TYPE, optional
            模型对象. The default is None.
        """
        if self.best_value is None:
            self.best_value = value
            self.best_model = deepcopy(model.state_dict())
        elif (self.best_value - value) * self.direction > self.delta:
            self.best_value = value
            self.best_model = deepcopy(model.state_dict())
            self.count = 0
        else:
            self.count += 1
            if self.verbose:
                self.logger.info("EarlyStop Counter: {:02d}".format(self.count))
            if self.count >= self.patience:
                self.is_earlystop = True
                self.logger.info("# EarlyStop!")

