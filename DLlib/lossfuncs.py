import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
import torch.optim as optim

# import torchsort
from DLlib.utils import *


class VectorLoss(nn.Module):
    def __init__(self, loss_type: str, pred_dim, label_dim) -> None:
        super().__init__()
        self.func = getattr(VectorLoss, loss_type)
        self.pred_dim = pred_dim
        self.label_dim = label_dim

    @staticmethod
    def mse(pred, label):
        loss = F.mse_loss(pred, label, reduction="none")
        return loss

    @staticmethod
    def rmse(pred, label):
        loss = F.mse_loss(pred, label, reduction="none")
        return loss**0.5

    @staticmethod
    def mae(pred, label):
        l1 = nn.L1Loss(reduction="none")
        loss = l1(
            pred,
            label,
        )
        return loss

    @staticmethod
    def huber(pred, label, delta=1):
        loss = F.huber_loss(pred, label, delta=delta, reduction="none")
        return loss

    @staticmethod
    def bce(pred, label):
        binary = nn.BCELoss(reduction="none")
        loss = binary(
            pred,
            label,
        )
        return loss

    @staticmethod
    def cross_entropy(pred, label):
        cel = nn.CrossEntropyLoss(reduction="none")
        loss = cel(
            pred,
            (label.reshape(-1) - label.reshape(-1).min()).long(),
        )
        return loss

    @staticmethod
    def mae_withoutSig(pred, label):
        # 不用 Sigmoid 的 MAE
        epsilon = 1e-8
        pred = torch.clamp(pred, epsilon, 1 - epsilon)
        label = torch.clamp(label, epsilon, 1 - epsilon)
        pred_withoutSig = -torch.log((1 / pred) - 1)
        label_withoutSig = -torch.log((1 / label) - 1)
        loss = VectorLoss.mae(pred_withoutSig, label_withoutSig)
        return loss

    @staticmethod
    def ic(pred, label):
        tensor = torch.cat([pred.reshape(1, -1), label.reshape(1, -1)], dim=0)
        ic_loss = 1 - torch.corrcoef(tensor)[0, 1]  # 1->0
        return ic_loss

    # @staticmethod
    # def rankic(pred, label):
    #     label = label.reshape(1, -1)
    #     pred = pred.reshape(1, -1)
    #     pred = torchsort.soft_rank(pred)
    #     label = torchsort.soft_rank(label)
    #     pred = pred - pred.mean()
    #     pred = pred / pred.norm()
    #     label = label - label.mean()
    #     label = label / label.norm()
    #     return -(pred * label).sum()

    @staticmethod
    def ccc(pred, label):
        mean_x = torch.mean(pred)
        mean_y = torch.mean(label)
        std_x = torch.std(pred, unbiased=False)
        std_y = torch.std(label, unbiased=False)
        # 皮尔逊相关系数
        cov_xy = torch.mean((pred - mean_x) * (label - mean_y))
        pearson_corr = cov_xy / (std_x * std_y)
        # CCC计算
        ccc = (2 * pearson_corr * std_x * std_y) / (std_x**2 + std_y**2 + (mean_x - mean_y) ** 2)
        ccc_loss = 1 - ccc
        return ccc_loss

    @staticmethod
    def cosine(pred, label):
        cos_sim = F.cosine_similarity(pred, label)
        loss = 1 - cos_sim
        return loss

    @staticmethod
    def sharpe(pred, label):
        # rolling_pred_max = np.max(pred_calib_list[-rolling_win:])
        # weight = pred_calibrate / (1.2 * rolling_pred_max)
        # pred_max = torch.max(pred)
        # weight = pred / (1.2 * pred_max)
        # return -torch.sum(weight * label)
        pred = pred - torch.mean(pred)
        weight = (torch.sigmoid(pred) - 0.5) * 2
        print(weight)
        ret_by_position = weight * label
        ret_mean = torch.mean(ret_by_position)
        ret_vol = torch.sqrt(torch.mean(ret_by_position**2) - ret_mean**2) + 1e-8
        sharpe = (ret_mean * torch.sqrt(torch.tensor(252.0, dtype=torch.float32))) / ret_vol
        return -sharpe

    def forward(self, pred, label, weights=None):
        label = label.reshape(-1, self.label_dim)
        pred = pred.reshape(-1, self.pred_dim)
        mask = torch.any(torch.isfinite(label), dim=1)
        loss = self.func(pred[mask], label[mask])
        if weights is not None and loss.dim() > 1:
            weights = weights.reshape(-1, 1)
            loss *= weights[mask]
        return loss.mean()


class MultiClassFocalLoss(nn.Module):
    def __init__(self, gamma=2, alpha=None):
        super().__init__()
        device = get_device(0)
        self.gamma = gamma
        self.alpha = torch.tensor(alpha).to(device)

    def forward(self, inputs, targets):
        """
        inputs = [batch_size, output_size] 已做softmax
        targets = [batch_size, 1]
        ce_loss = [batch_size, output_size]
        """
        output_size = inputs.shape[-1]
        targets = (targets - targets.min()).long()
        targets_one_hot = F.one_hot(targets, num_classes=output_size).float()
        ce_loss = F.binary_cross_entropy(inputs, targets_one_hot, reduction="none")
        p_t = torch.exp(-ce_loss)
        modulating_factor = (1 - p_t) ** self.gamma  # 概率越大（预测越准），对Loss贡献越小
        # Apply class weights if alpha is provided
        if self.alpha is not None:
            modulating_factor = modulating_factor * self.alpha[targets].reshape(modulating_factor.shape[0], 1)
        focal_loss = (ce_loss * modulating_factor).mean()
        return focal_loss


class ICLoss(nn.Module):
    def __init__(self, penalty_alpha, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.penalty_alpha = penalty_alpha

    @staticmethod
    def mutual_corrcoef(x, y):
        """传入向量x[m,1], y[m,1]，求取相关系数 r"""
        nan_mask = torch.isnan(x) | torch.isnan(y)
        x, y = x[~nan_mask], y[~nan_mask]
        f = (x.shape[0] - 1) / x.shape[0]  # 方差调整系数
        x_mean, x_var = x.mean(), x.var()
        y_mean, y_var = y.mean(), y.var()
        numerator = ((x - x_mean) * (y - y_mean)).mean()
        denominator = torch.sqrt(x_var * y_var) * f
        return numerator / denominator

    def forward(self, pred, label):
        """
        pred: shape(m, n), n = hidden layers
        label: shape(m, 1)
        需要做自相关系数的惩罚
        """
        coef_matrix = torch.corrcoef(pred.T)  # 自相关系数
        penalty = (coef_matrix.sum() - coef_matrix.shape[0]) * self.penalty_alpha  # 自相关系数之和
        pred = pred.mean(axis=1)  # 按行求均值
        base_loss = self.mutual_corrcoef(pred, label)
        return -(base_loss - penalty)


class MultiTaskUncertaintyLoss(nn.Module):
    # 自适应调整两个任务的损失权重
    def __init__(self, delta=1.0, eps=1e-6, pred_dim=1, label_dim=1):
        super().__init__()
        self.delta = delta  # for Huber loss
        self.eps = eps
        self.pred_dim = pred_dim
        self.label_dim = label_dim
        # Learnable log-variance parameters (log σ^2)
        self.log_sigma1 = nn.Parameter(torch.tensor(0.0))  # for Huber
        self.log_sigma2 = nn.Parameter(torch.tensor(0.0))  # for Pearson

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        target = target.reshape(-1, self.label_dim)
        pred = pred.reshape(-1, self.pred_dim)

        # --- Huber Loss ---
        huber_loss = F.huber_loss(pred, target, delta=self.delta, reduction="mean")

        # --- Pearson Loss ---
        pred_centered = pred - pred.mean()
        target_centered = target - target.mean()
        corr = torch.sum(pred_centered * target_centered) / (
            torch.sqrt(torch.sum(pred_centered**2) + self.eps) * torch.sqrt(torch.sum(target_centered**2) + self.eps)
        )
        pearson_loss = 1.0 - corr  # range: [0, 2]

        # --- Multi-task Uncertainty Weighting ---
        loss = (
            torch.exp(-self.log_sigma1) * huber_loss
            + torch.exp(-self.log_sigma2) * pearson_loss
            + self.log_sigma1
            + self.log_sigma2
        )
        return loss


class EnsembleLearningLoss(nn.Module):
    def __init__(
        self,
        update_time,
        alpha=1.0,
        beta=1.0,
    ):
        """
        alpha, beta: 自适应学习函数权重系数
        update_time: 学习多少个batch得到mae_level，一般选择一个epoch的batch
        """
        super(EnsembleLearningLoss, self).__init__()
        self.alpha = nn.Parameter(torch.tensor(alpha, requires_grad=True))
        self.beta = nn.Parameter(torch.tensor(beta, requires_grad=True))
        self.count = self.cumuloss = 0
        self.update_time = update_time
        self.mae_level = None  # mae的量纲调整点

    def forward(self, y_true, y_pred):
        mae_loss = torch.mean(torch.abs(y_true - y_pred))
        if self.mae_level is not None:
            normalized_mae = mae_loss / (self.mae_level + 1e-8)
            tensor = torch.cat([y_pred.reshape(1, -1), y_true.reshape(1, -1)], dim=0)
            ic_loss = torch.corrcoef(tensor)[0, 1]  # ic loss
            normalized_ic = 1 - ic_loss  # 1->0
            loss = (
                1 / (2 * torch.square(self.alpha)) * normalized_mae
                + 1 / (2 * torch.square(self.beta)) * normalized_ic
                + torch.log(self.alpha * self.beta)
            )
        else:
            loss = mae_loss
        self.count += 1
        self.cumuloss += loss.item()
        if self.count == self.update_time:
            if self.mae_level is None:
                self.mae_level = self.cumuloss / self.update_time
        return loss


class EnsembleSwitchLoss(nn.Module):
    def __init__(self, alpha=0.8, beta=3.0):
        """
        alpha: [0, 1]之间，自适应学习函数权重系数，越大表示初始情况使用更多的mae
        beta: 标量，越大表示alpha衰减的越快
        """
        super(EnsembleSwitchLoss, self).__init__()
        self.alpha = nn.Parameter(torch.tensor(alpha, requires_grad=True))
        self.beta = beta
        self.count = self.cumuloss = 0
        self.loss_level = None  # 单个batch的loss量纲

    def forward(self, y_true, y_pred):
        loss = torch.mean(torch.abs(y_true - y_pred))
        if self.loss_level is not None:
            normalized_loss = loss / (self.loss_level + 1e-8)  # 除以loss level会让模型走的太猛？
            tensor = torch.cat([y_pred.reshape(1, -1), y_true.reshape(1, -1)], dim=0)
            ic_loss = torch.corrcoef(tensor)[0, 1]
            normalized_ic = 1 - ic_loss  # 1->0
            # loss = self.alpha * normalized_loss + (1-self.alpha) * normalized_ic
            loss = self.alpha * normalized_loss + (1 - self.alpha) * normalized_ic

        self.count += 1
        self.cumuloss += loss.item()
        return loss

    def update_lossfunc(
        self,
    ):
        # 第一次update，切换损失函数
        mean_loss = self.cumuloss / self.count
        if self.loss_level is None:
            self.loss_level = mean_loss
        # 之后的每次update，调整权重
        else:
            # 更新alpha值: 希望让alpha由1逐渐递减，衰减速度与当前error有关
            scale = min(max(0.8, mean_loss), 1)  # scale在0.8-1之间
            self.alpha.data = max(
                torch.tensor(0.0),
                1 / (1 + np.exp(-self.beta)) * scale * self.alpha.data,
            )
            print(scale, self.alpha.data)
        self.count = self.cumuloss = 0


if __name__ == "__main__":
    # # 损失函数测试
    # pred = torch.rand(10, 1)
    # label = pred+0.01
    # loss = VectorLoss('mse')
    # print(loss(pred, label))

    # 生成一些示例数据
    batch_size = 256
    seq_length = 20
    input_dim = 1
    model_dim = 64
    num_heads = 4
    num_layers = 2
    output_dim = 1

    torch.manual_seed(5)
    x = torch.rand(batch_size, seq_length, input_dim)
    torch.manual_seed(42)
    y = torch.rand(batch_size, seq_length, output_dim)

    class SimpleTransformer(nn.Module):
        def __init__(self, input_dim, model_dim, num_heads, num_layers, output_dim):
            super(SimpleTransformer, self).__init__()
            self.encoder_layer = nn.TransformerEncoderLayer(d_model=model_dim, nhead=num_heads, batch_first=True)
            self.transformer_encoder = nn.TransformerEncoder(self.encoder_layer, num_layers=num_layers)
            self.linear = nn.Linear(model_dim, output_dim)
            self.input_linear = nn.Linear(input_dim, model_dim)

        def forward(self, src):
            src = self.input_linear(src)
            output = self.transformer_encoder(src)
            output = self.linear(output)
            return output

    model = SimpleTransformer(input_dim, model_dim, num_heads, num_layers, output_dim)
    # gr_loss = EnsembleLearningLoss(update_time=1, alpha=1.0, beta=1.0)
    # optimizer = optim.Adam(
    #     list(model.parameters()) + list(gr_loss.parameters()), lr=0.01
    # )
    gr_loss = VectorLoss("sharpe", 1, 1)
    optimizer = optim.Adam(model.parameters(), lr=0.1)
    # 训练循环
    num_epochs = 1000
    for epoch in range(num_epochs):
        optimizer.zero_grad()
        y_pred = model(x)
        loss = gr_loss(y, y_pred)
        loss.backward()
        optimizer.step()
        # print(
        #     f"Epoch {epoch+1}/{num_epochs}, Loss: {loss.item()}, Alpha: {gr_loss.alpha.item()}, beta: {gr_loss.beta}"
        # )
        print(f"Epoch {epoch+1}/{num_epochs}, Loss: {loss.item()}")
    # normalized ic loss
    tensor = torch.cat([model(x).reshape(1, -1), y.reshape(1, -1)], dim=0)
    ic_loss = torch.corrcoef(tensor)[0, 1]  # ic loss
    print(ic_loss.item())
    # print("训练完成后的alpha值:", gr_loss.alpha.item())
